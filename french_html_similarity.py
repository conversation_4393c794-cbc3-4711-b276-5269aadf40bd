import asyncio
import itertools
from functools import partial

# https://github.com/GateNLP/ultimate-sitemap-parser
from usp.tree import sitemap_tree_for_homepage

# https://github.com/UKPLab/sentence-transformers
from sentence_transformers import SentenceTransformer, util

# https://github.com/trafilatura/trafilatura
import trafilatura

# https://github.com/networkx/networkx
import networkx as nx

# https://github.com/Textualize/rich
from rich.console import Console
from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
)
from rich.panel import Panel

# https://github.com/autoscrape-labs/pydoll
from pydoll.browser import Chrome
from pydoll.protocol.network.events import NetworkEvent

console = Console()


async def scrape_page(url, tab):
    console.print(f"🔍 Extraction du contenu de la page: '{url}'...")

    await tab.go_to(url)
    title = (await tab.execute_script("return document.title"))['result']['result']['value']
    html_code = (await tab.execute_script("return document.documentElement.outerHTML"))['result']['result']['value']
    links = await tab.find(tag_name="a", find_all=True)
    return {
        "url": url,
        "title": title,
        "html_code": html_code,
        "link_count": len(links),
    }




async def scraping(urls):
    async with Chrome() as browser:
        site_responses = {}

        async def on_response(tab, event):
            url = event['params']['response']['url']
            status = event['params']['response']['status']

            title = (await tab.execute_script("return document.title"))['result']['result']['value']
            html_code = (await tab.execute_script("return document.documentElement.outerHTML"))['result']['result']['value']
            links = await tab.find(tag_name="a", find_all=True)

            site_responses[url] = {
                "url": url,
                "title": title,
                "html_code": html_code,
                "link_count": len(links),
                "status_code": status,
            }

        tab = await browser.start(headless=True)

        await tab.enable_network_events()
        await tab.on(NetworkEvent.RESPONSE_RECEIVED, partial(on_response, tab))

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("Extraction en cours...", total=len(urls))

            for url in urls:
                progress.update(task, description=f"Traitement: {url[:50]}...")

                console.print(f"🔍 Extraction du contenu de la page: '{url}'...")
                await tab.go_to(url)

                progress.advance(task)
                #yield await scrape_page(url, tab)

                return site_responses

async def main():
    console.print(
        Panel.fit("🚀 Analyseur de Similarité HTML Français", style="bold magenta")
    )

    console.print("[cyan]📡 Récupération des URLs depuis le sitemap...[/cyan]")
    site_urls = []

    #site_url ="https://zonetuto.fr/"
    site_url = "http://cocon.se/"
    #site_url = "https://www.patrickcoquart.com/"

    sitemap_tree = sitemap_tree_for_homepage(
        homepage_url=site_url,
        use_known_paths=False,
    )
    if len(list(sitemap_tree.all_pages())) == 0:
        sitemap_tree = sitemap_tree_for_homepage(
            homepage_url=site_url,
            use_known_paths=True,
        )

    for page in sitemap_tree.all_pages():
        site_urls.append(page.url)
    console.print(f"[green]✅ {len(site_urls)} URLs trouvées dans le sitemap[/green]")

    scraped_data = []
    async for page_data in scraping(site_urls):
        scraped_data.append(page_data)

    console.print(f"[green]✅ {len(scraped_data)} pages scrapées avec succès[/green]")


if __name__ == "__main__":
    asyncio.run(main())
